"""
简单的 API 测试脚本
用于验证修改后的 RESTful API 接口
"""

import sys
import os
import asyncio

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# 测试导入
try:
    from backend.app import app, create_success_response, create_error_response
    print("✅ 成功导入 app 和响应函数")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

# 测试响应格式函数
def test_response_functions():
    print("\n=== 测试响应格式函数 ===")
    
    # 测试成功响应
    success_response = create_success_response(
        data={"test": "data"},
        message="Test successful",
        code=200
    )
    print("✅ 成功响应格式:", success_response)
    
    # 测试错误响应
    error_response = create_error_response(
        message="Test error",
        code=400
    )
    print("✅ 错误响应格式:", error_response)

# 测试 FastAPI 应用
def test_fastapi_app():
    print("\n=== 测试 FastAPI 应用 ===")
    
    # 检查应用是否正确创建
    if app:
        print("✅ FastAPI 应用创建成功")
        
        # 检查路由
        routes = [route.path for route in app.routes]
        print(f"✅ 应用包含 {len(routes)} 个路由")
        
        # 检查新的 RESTful 路由是否存在
        new_routes = [
            "/web/users",
            "/web/auth/sessions", 
            "/vtubers/{vtuber_name}/mid",
            "/vtubers/{vtuber_name}/followers/current",
            "/vtubers/{vtuber_name}/followers/history"
        ]
        
        for route in new_routes:
            if any(route in r for r in routes):
                print(f"✅ 新路由存在: {route}")
            else:
                print(f"❌ 新路由缺失: {route}")
        
        # 检查旧路由是否仍然存在（向后兼容）
        old_routes = [
            "/web/users/register",
            "/web/users/login",
            "/basic/mid",
            "/basic/follower/current",
            "/basic/follower/all"
        ]
        
        for route in old_routes:
            if any(route in r for r in routes):
                print(f"✅ 旧路由保留（向后兼容）: {route}")
            else:
                print(f"❌ 旧路由缺失: {route}")
    else:
        print("❌ FastAPI 应用创建失败")

# 测试模型导入
def test_models():
    print("\n=== 测试模型导入 ===")
    
    try:
        from backend.app import ApiResponse, ApiError, PaginatedResponse
        print("✅ 成功导入新的响应模型")
        
        # 测试模型创建
        api_response = ApiResponse(code=200, message="test", data={"key": "value"})
        print("✅ ApiResponse 模型创建成功")
        
        api_error = ApiError(code=400, message="error")
        print("✅ ApiError 模型创建成功")
        
    except ImportError as e:
        print(f"❌ 模型导入失败: {e}")

def main():
    print("🚀 开始 RESTful API 修改验证测试")
    
    test_response_functions()
    test_fastapi_app()
    test_models()
    
    print("\n✅ 测试完成！")

if __name__ == "__main__":
    main()
